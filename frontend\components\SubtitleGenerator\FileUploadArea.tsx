/**
 * 文件上传区域组件
 * 支持拖拽上传和点击选择文件
 */

"use client"

import React, { useState, useCallback, useRef } from 'react'
import { Upload, File, X, AlertCircle, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'

import { SubtitleService } from '@/lib/subtitle-service'
import { CONSTANTS } from './types'
import type { FileValidation } from './types'

interface FileUploadAreaProps {
  onFileSelect: (file: File) => void
  selectedFile: File | null
  validation: FileValidation
  disabled?: boolean
}

const FileUploadArea: React.FC<FileUploadAreaProps> = ({
  onFileSelect,
  selectedFile,
  validation,
  disabled = false
}) => {
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理文件选择
  const handleFileSelect = useCallback((file: File) => {
    onFileSelect(file)
  }, [onFileSelect])

  // 处理拖拽进入
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!disabled) {
      setIsDragOver(true)
    }
  }, [disabled])

  // 处理拖拽离开
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }, [])

  // 处理拖拽悬停
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  // 处理文件拖拽放置
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    if (disabled) return

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [disabled, handleFileSelect])

  // 处理点击选择文件
  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [disabled])

  // 处理文件输入变化
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  // 清除选择的文件
  const handleClearFile = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    // 这里我们不直接调用onFileSelect(null)，而是让父组件处理
  }, [])

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    return SubtitleService.formatFileSize(bytes)
  }

  // 获取文件类型图标
  const getFileTypeIcon = (file: File) => {
    if (file.type.startsWith('video/')) {
      return '🎬'
    } else if (file.type.startsWith('audio/')) {
      return '🎵'
    }
    return '📄'
  }

  return (
    <div className="space-y-4">
      {/* 文件上传区域 */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300 cursor-pointer
          ${isDragOver 
            ? 'border-purple-500 bg-purple-50' 
            : validation.isValid 
              ? 'border-gray-300 hover:border-purple-400 hover:bg-gray-50' 
              : 'border-red-300 bg-red-50'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={CONSTANTS.SUPPORTED_FILE_EXTENSIONS.join(',')}
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        {selectedFile ? (
          // 已选择文件的显示
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <div className="text-4xl mb-2">
                {getFileTypeIcon(selectedFile)}
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-semibold text-lg text-gray-900">
                {selectedFile.name}
              </h3>
              <p className="text-sm text-gray-600">
                大小: {formatFileSize(selectedFile.size)}
              </p>
              <p className="text-sm text-gray-600">
                类型: {selectedFile.type || '未知'}
              </p>
            </div>

            {/* 验证状态指示 */}
            <div className="flex items-center justify-center gap-2">
              {validation.isValid ? (
                <>
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-sm text-green-600">文件验证通过</span>
                </>
              ) : (
                <>
                  <AlertCircle className="w-5 h-5 text-red-500" />
                  <span className="text-sm text-red-600">文件验证失败</span>
                </>
              )}
            </div>

            {/* 重新选择按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                handleClearFile()
                handleClick()
              }}
              disabled={disabled}
            >
              重新选择文件
            </Button>
          </div>
        ) : (
          // 未选择文件的显示
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <Upload className={`w-12 h-12 ${isDragOver ? 'text-purple-500' : 'text-gray-400'}`} />
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {isDragOver ? '释放文件以上传' : '拖拽文件到此处或点击选择'}
              </h3>
              <p className="text-sm text-gray-600">
                支持的格式: MP3, WAV, FLAC, M4A, AAC, OGG
              </p>
              <p className="text-sm text-gray-600">
                最大文件大小: {formatFileSize(CONSTANTS.MAX_FILE_SIZE)}
              </p>
            </div>

            <Button
              variant="outline"
              className="mt-4"
              disabled={disabled}
            >
              <File className="w-4 h-4 mr-2" />
              选择文件
            </Button>
          </div>
        )}
      </div>

      {/* 验证错误信息 */}
      {!validation.isValid && validation.error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validation.error}</AlertDescription>
        </Alert>
      )}

      {/* 验证警告信息 */}
      {validation.isValid && validation.warnings && validation.warnings.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {validation.warnings.join(', ')}
          </AlertDescription>
        </Alert>
      )}

      {/* 文件格式说明 */}
      <div className="text-xs text-gray-500 space-y-1">
        <p><strong>支持的音频格式:</strong> MP3, WAV, FLAC, M4A, AAC, OGG</p>
        {/* <p><strong>支持的视频格式:</strong> MP4, MOV</p> */}
        <p><strong>建议:</strong> 为获得最佳效果，请使用清晰的音频文件，避免背景噪音过大</p>
      </div>
    </div>
  )
}

export default FileUploadArea
