/**
 * 字幕生成按钮组件
 * 用于导航栏的字幕生成功能入口
 */

"use client"

import React from 'react'
import { FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface SubtitleButtonProps {
  onClick: () => void
  disabled?: boolean
  className?: string
}

const SubtitleButton: React.FC<SubtitleButtonProps> = ({
  onClick,
  disabled = false,
  className = ""
}) => {
  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      className={`
        group relative 
        bg-gradient-to-r from-purple-500 via-violet-500 to-indigo-500 
        hover:from-purple-600 hover:via-violet-600 hover:to-indigo-600 
        text-white px-3 py-2 lg:px-5 lg:py-3 
        rounded-2xl shadow-lg hover:shadow-2xl 
        transition-all duration-500 transform hover:scale-110 
        border-0 overflow-hidden
        ${className}
      `}
      title="字幕生成"
    >
      {/* 背景光效 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
      
      {/* 按钮内容 */}
      <div className="relative z-10 flex items-center gap-2">
        <FileText className="w-4 h-4 lg:w-5 lg:h-5" />
        <span className="hidden sm:inline font-semibold text-sm lg:text-base">
          字幕生成
        </span>
      </div>
      
      {/* 悬停时的额外光晕效果 */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-400/30 via-violet-400/30 to-indigo-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl" />
    </Button>
  )
}

export default SubtitleButton
