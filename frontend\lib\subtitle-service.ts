/**
 * 字幕生成服务
 * 封装字幕生成相关的API调用逻辑
 */

import { API_ENDPOINTS } from './api'
import * as SubtitleTypes from '@/components/SubtitleGenerator/types'
import type {
  SubtitleConfig,
  UploadResponse,
  StatusResponse,
  FileValidation
} from '@/components/SubtitleGenerator/types'

// 字幕服务配置
const SUBTITLE_API_CONFIG = {
  // 字幕服务API基础URL
  BASE_URL: process.env.NEXT_PUBLIC_SUBTITLE_API_URL || 'http://localhost:8000',
  // 字幕服务API密钥 (独立认证)
  API_KEY: process.env.NEXT_PUBLIC_SUBTITLE_API_KEY || '',
  // 请求超时时间
  TIMEOUT: 30000, // 30秒
  // 最大重试次数
  MAX_RETRIES: 3
}

// 获取字幕API的完整URL
function getSubtitleApiUrl(endpoint: string): string {
  return `${SUBTITLE_API_CONFIG.BASE_URL}${endpoint}`
}

// 创建字幕API请求头
function createSubtitleApiHeaders(includeContentType: boolean = false): HeadersInit {
  const headers: HeadersInit = {
    'Authorization': `Bearer ${SUBTITLE_API_CONFIG.API_KEY}`,
  }

  if (includeContentType) {
    headers['Content-Type'] = 'application/json'
  }

  return headers
}

// 验证字幕API配置
function validateSubtitleApiConfig(): void {
  if (!SUBTITLE_API_CONFIG.BASE_URL) {
    console.error('❌ 字幕API配置错误: NEXT_PUBLIC_SUBTITLE_API_URL 未设置')
    throw new Error('字幕服务配置错误，请检查环境变量 NEXT_PUBLIC_SUBTITLE_API_URL')
  }

  if (!SUBTITLE_API_CONFIG.API_KEY) {
    console.error('❌ 字幕API密钥配置错误: NEXT_PUBLIC_SUBTITLE_API_KEY 未设置')
    throw new Error('字幕服务API密钥未配置，请检查环境变量 NEXT_PUBLIC_SUBTITLE_API_KEY')
  }

  // 字幕API配置验证完成
}

export class SubtitleService {
  /**
   * 初始化服务并验证配置
   */
  static init(): void {
    validateSubtitleApiConfig()
  }

  /**
   * 验证文件是否符合要求
   */
  static validateFile(file: File): FileValidation {
    const validation: FileValidation = {
      isValid: true,
      warnings: []
    }

    // 检查文件大小
    if (file.size > SubtitleTypes.CONSTANTS.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: SubtitleTypes.ERROR_MESSAGES.FILE_TOO_LARGE
      }
    }

    // 检查文件格式
    const isValidFormat = SubtitleTypes.SUPPORTED_FILE_FORMATS.includes(file.type as any) ||
      SubtitleTypes.CONSTANTS.SUPPORTED_FILE_EXTENSIONS.some(ext =>
        file.name.toLowerCase().endsWith(ext)
      )

    if (!isValidFormat) {
      return {
        isValid: false,
        error: SubtitleTypes.ERROR_MESSAGES.UNSUPPORTED_FORMAT
      }
    }

    // 添加警告信息
    if (file.size > 100 * 1024 * 1024) { // 100MB
      validation.warnings?.push('大文件可能需要较长处理时间')
    }

    return validation
  }

  /**
   * 上传音频文件并开始转录任务
   */
  static async uploadAudio(
    file: File,
    config: SubtitleConfig
  ): Promise<UploadResponse> {
    try {
      const formData = new FormData()
      formData.append('file', file)

      // 添加配置参数
      if (config.language_code) {
        formData.append('language_code', config.language_code)
      }
      formData.append('tag_audio_events', config.tag_audio_events.toString())
      formData.append('timestamps_granularity', config.timestamps_granularity)
      // additional_formats 是必需参数
      formData.append('additional_formats', config.additional_formats)

      // 使用字幕服务的API密钥认证
      const response = await fetch(getSubtitleApiUrl(API_ENDPOINTS.SUBTITLE.UPLOAD), {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUBTITLE_API_CONFIG.API_KEY}`,
        },
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('字幕上传失败:', error)
      throw error
    }
  }

  /**
   * 查询任务状态
   */
  static async getTaskStatus(taskId: string): Promise<StatusResponse> {
    try {
      const response = await fetch(getSubtitleApiUrl(`${API_ENDPOINTS.SUBTITLE.STATUS}/${taskId}`), {
        method: 'GET',
        headers: createSubtitleApiHeaders(true),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('查询任务状态失败:', error)
      throw error
    }
  }

  /**
   * 下载SRT文件
   */
  static async downloadSRT(taskId: string, filename: string): Promise<Blob> {
    try {
      const url = getSubtitleApiUrl(`${API_ENDPOINTS.SUBTITLE.DOWNLOAD}/${taskId}/${filename}`)
      const headers = createSubtitleApiHeaders()

      const response = await fetch(url, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('[SUBTITLE-SERVICE] Download failed:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()
      return blob
    } catch (error) {
      console.error('[SUBTITLE-SERVICE] 下载SRT文件失败:', error)
      throw error
    }
  }

  /**
   * 轮询任务状态直到完成
   */
  static async pollTaskStatus(
    taskId: string,
    onProgress: (status: StatusResponse) => void,
    onComplete: (status: StatusResponse) => void,
    onError: (error: Error) => void,
    maxAttempts: number = SubtitleTypes.CONSTANTS.MAX_POLLING_ATTEMPTS
  ): Promise<void> {
    let attempts = 0
    
    const poll = async () => {
      try {
        attempts++
        
        if (attempts > maxAttempts) {
          throw new Error(SubtitleTypes.ERROR_MESSAGES.POLLING_TIMEOUT)
        }

        const status = await this.getTaskStatus(taskId)
        
        // 调用进度回调
        onProgress(status)
        
        if (status.status === 'completed') {
          onComplete(status)
          return
        }
        
        if (status.status === 'failed') {
          throw new Error(status.error || SubtitleTypes.ERROR_MESSAGES.TASK_FAILED)
        }
        
        // 如果还在处理中，继续轮询
        if (status.status === 'processing' || status.status === 'pending') {
          setTimeout(poll, SubtitleTypes.CONSTANTS.POLLING_INTERVAL)
        }
        
      } catch (error) {
        onError(error instanceof Error ? error : new Error('未知错误'))
      }
    }
    
    // 开始轮询
    poll()
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 格式化处理时间
   */
  static formatProcessingTime(seconds: number): string {
    if (seconds < 60) {
      return `${Math.round(seconds)}秒`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.round(seconds % 60)
      return `${minutes}分${remainingSeconds}秒`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}小时${minutes}分钟`
    }
  }

  /**
   * 生成下载文件名
   */
  static generateDownloadFilename(originalFilename: string, taskId: string): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
    const baseName = originalFilename.replace(/\.[^/.]+$/, '') // 移除扩展名
    return `${baseName}_${timestamp}_${taskId.slice(0, 8)}.srt`
  }
}

// 导出便捷方法
export const subtitleService = SubtitleService
