✅ 字幕API配置: ObjectapiKeyConfigured: trueapiKeyPrefix: "OwxMwzoo..."baseUrl: "http://localhost:8000"maxRetries: 3timeout: 30000[[Prototype]]: Object

[SUBTITLE-MODAL] Task status response: Object
D:\myaitts-worker\frontend\components\SubtitleGenerator\SubtitleGeneratorModal.tsx:200 [SUBTITLE-MODAL] Task status response: Object
D:\myaitts-worker\frontend\components\SubtitleGenerator\SubtitleGeneratorModal.tsx:200 [SUBTITLE-MODAL] Task status response: Object
D:\myaitts-worker\frontend\components\SubtitleGenerator\SubtitleGeneratorModal.tsx:216 [SUBTITLE-MODAL] Task completed, srt_files: Array(1)
D:\myaitts-worker\frontend\components\SubtitleGenerator\SubtitleGeneratorModal.tsx:225 [SUBTITLE-MODAL] Notifying task completion to parent: Object
D:\myaitts-worker\frontend\app\page.tsx:1456 [MAIN-PAGE] Subtitle task completed, updating task center: Object
D:\myaitts-worker\frontend\components\TaskCenter.tsx:137 [TASK-CENTER] Updating task status: Object
D:\myaitts-worker\frontend\lib\subtitle-service.ts:183 [SUBTITLE-SERVICE] Downloading SRT: Object
D:\myaitts-worker\frontend\lib\subtitle-service.ts:190 [SUBTITLE-SERVICE] Download response: Object
D:\myaitts-worker\frontend\lib\subtitle-service.ts:199 [SUBTITLE-SERVICE] Download successful, blob size: 1764
[TASK-CENTER] Initiating download for task: Object
D:\myaitts-worker\frontend\components\TaskCenter.tsx:314 [TASK-CENTER] Subtitle task has 1 files: Array(1)
D:\myaitts-worker\frontend\components\TaskCenter.tsx:317 [TASK-CENTER] Downloading subtitle file: 494f0e22-decd-4f0e-a4f6-124a5addee3b.srt
D:\myaitts-worker\frontend\lib\subtitle-service.ts:183 [SUBTITLE-SERVICE] Downloading SRT: Object
D:\myaitts-worker\frontend\lib\subtitle-service.ts:190 [SUBTITLE-SERVICE] Download response: Object
D:\myaitts-worker\frontend\lib\subtitle-service.ts:199 [SUBTITLE-SERVICE] Download successful, blob size: 1764
D:\myaitts-worker\frontend\components\TaskCenter.tsx:330 [TASK-CENTER] Subtitle file downloaded: 494f0e22-decd-4f0e-a4f6-124a5addee3b.srt
[SUBTITLE-MODAL] Task status response: {taskId: '4026dc32-96d0-445d-9316-46fde3312ebe', status: 'processing', progress: 10, srtFilesCount: 0, hasOnTaskCompleted: true}
D:\myaitts-worker\frontend\components\SubtitleGenerator\SubtitleGeneratorModal.tsx:200 [SUBTITLE-MODAL] Task status response: {taskId: '4026dc32-96d0-445d-9316-46fde3312ebe', status: 'processing', progress: 10, srtFilesCount: 0, hasOnTaskCompleted: true}
D:\myaitts-worker\frontend\components\SubtitleGenerator\SubtitleGeneratorModal.tsx:200 [SUBTITLE-MODAL] Task status response: {taskId: '4026dc32-96d0-445d-9316-46fde3312ebe', status: 'completed', progress: 100, srtFilesCount: 1, hasOnTaskCompleted: true}
D:\myaitts-worker\frontend\components\SubtitleGenerator\SubtitleGeneratorModal.tsx:216 [SUBTITLE-MODAL] Task completed, srt_files: [{…}]
D:\myaitts-worker\frontend\components\SubtitleGenerator\SubtitleGeneratorModal.tsx:225 [SUBTITLE-MODAL] Notifying task completion to parent: {taskId: '4026dc32-96d0-445d-9316-46fde3312ebe', srtFiles: Array(1)}
D:\myaitts-worker\frontend\app\page.tsx:1456 [MAIN-PAGE] Subtitle task completed, updating task center: {taskId: '4026dc32-96d0-445d-9316-46fde3312ebe', status: 'complete', srtFilesCount: 1}
D:\myaitts-worker\frontend\components\TaskCenter.tsx:137 [TASK-CENTER] Updating task status: {taskId: '4026dc32-96d0-445d-9316-46fde3312ebe', status: 'complete', hasDownloadUrl: false, srtFilesCount: 1}