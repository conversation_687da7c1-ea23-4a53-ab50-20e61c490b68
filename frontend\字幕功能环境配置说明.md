# 字幕生成功能环境配置说明

## 📋 概述

字幕生成功能使用独立的后端服务，与TTS功能分离部署。需要单独配置API地址。

## 🔧 环境变量配置

### 必需的环境变量

```bash
# 字幕生成服务API地址
NEXT_PUBLIC_SUBTITLE_API_URL=<字幕后端服务地址>

# 字幕生成服务API密钥 (独立认证)
NEXT_PUBLIC_SUBTITLE_API_KEY=<字幕服务API密钥>
```

### 不同环境的配置示例

#### 本地开发环境
```bash
# .env.local
NEXT_PUBLIC_SUBTITLE_API_URL=http://localhost:8000
NEXT_PUBLIC_SUBTITLE_API_KEY=OwxMwzoo5YieYWaPQUFd1kPg8h0k0rXecEGo6310
```

#### 测试环境
```bash
# .env.test
NEXT_PUBLIC_SUBTITLE_API_URL=https://test-subtitle-api.yourdomain.com
NEXT_PUBLIC_SUBTITLE_API_KEY=your_test_api_key_here
```

#### 生产环境
```bash
# .env.production
NEXT_PUBLIC_SUBTITLE_API_URL=https://subtitle-api.yourdomain.com
NEXT_PUBLIC_SUBTITLE_API_KEY=your_production_api_key_here
```

## 🏗️ 服务架构

### 服务分离说明
- **TTS服务**: 负责语音合成功能
  - 环境变量: `NEXT_PUBLIC_API_URL`
  - 认证方式: 用户登录Token
  - 示例地址: `https://api.myaitts.com`

- **字幕服务**: 负责音频转文字功能
  - 环境变量: `NEXT_PUBLIC_SUBTITLE_API_URL`, `NEXT_PUBLIC_SUBTITLE_API_KEY`
  - 认证方式: API密钥认证 (`Authorization: Bearer <api_key>`)
  - 示例地址: `http://localhost:8000` (开发) / `https://subtitle-api.yourdomain.com` (生产)

### API端点
字幕服务需要实现以下端点：

```
POST /api/v1/transcribe/upload      # 上传音频文件
GET  /api/v1/transcribe/status/{id} # 查询任务状态
GET  /api/v1/transcribe/download/{id}/{filename} # 下载字幕文件
```

## 🚀 部署配置

### 1. 本地开发
确保字幕后端服务在 `localhost:8000` 运行，或修改 `.env.local` 中的地址。

### 2. 生产部署
1. 部署字幕后端服务到生产环境
2. 获取生产环境的API地址
3. 在部署平台设置环境变量 `NEXT_PUBLIC_SUBTITLE_API_URL`

### 3. Vercel 部署示例
在 Vercel 项目设置中添加环境变量：
```
NEXT_PUBLIC_SUBTITLE_API_URL = https://your-subtitle-api.com
```

### 4. Docker 部署示例
```dockerfile
ENV NEXT_PUBLIC_SUBTITLE_API_URL=https://subtitle-api.yourdomain.com
```

## 🔍 配置验证

### 自动验证
系统会在字幕功能初始化时自动验证配置：
- 检查环境变量是否设置
- 输出配置信息到控制台
- 配置错误时显示用户友好的错误信息

### 手动验证
在浏览器控制台查看配置信息：
```javascript
console.log('字幕API地址:', process.env.NEXT_PUBLIC_SUBTITLE_API_URL)
```

## ⚠️ 常见问题

### 1. 环境变量未生效
**问题**: 修改了 `.env.local` 但配置未生效
**解决**: 重启开发服务器 `npm run dev`

### 2. API连接失败
**问题**: 字幕功能报网络错误
**检查**:
- 环境变量是否正确设置
- 字幕后端服务是否正常运行
- 网络连接是否正常
- CORS配置是否正确

### 3. 配置冲突
**问题**: TTS和字幕功能使用了相同的API地址
**解决**: 确保使用不同的环境变量：
- TTS: `NEXT_PUBLIC_API_URL`
- 字幕: `NEXT_PUBLIC_SUBTITLE_API_URL`

## 📝 配置检查清单

部署前请确认：

- [ ] 字幕后端服务已部署并正常运行
- [ ] 环境变量 `NEXT_PUBLIC_SUBTITLE_API_URL` 已正确设置
- [ ] API端点路径与后端实现一致
- [ ] CORS配置允许前端域名访问
- [ ] 认证机制与现有系统兼容
- [ ] 网络防火墙允许相关端口访问

## 🔗 相关文件

- **环境配置**: `frontend/.env.local`, `frontend/.env.example`
- **服务实现**: `frontend/lib/subtitle-service.ts`
- **API端点定义**: `frontend/lib/api.ts`
- **类型定义**: `frontend/components/SubtitleGenerator/types.ts`

## 📞 技术支持

如果遇到配置问题，请检查：
1. 浏览器控制台的错误信息
2. 网络请求的响应状态
3. 后端服务的日志
4. 环境变量的实际值
