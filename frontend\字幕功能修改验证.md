# 字幕功能认证修改验证

## 🔧 修改总结

### **问题解决**
- ✅ **移除了TTS认证系统依赖**: 不再使用 `AuthService.withTokenRefresh`
- ✅ **实现了独立API密钥认证**: 使用 `Authorization: Bearer <api_key>`
- ✅ **分离了认证系统**: TTS和字幕服务使用不同的认证机制
- ✅ **保持了TTS功能完整性**: 没有影响现有TTS功能

### **修改的文件**

#### 1. **环境配置文件**
- **`.env.local`**: 添加了 `NEXT_PUBLIC_SUBTITLE_API_KEY`
- **`.env.example`**: 添加了API密钥配置示例

#### 2. **核心服务文件**
- **`subtitle-service.ts`**: 
  - 移除了 `AuthService` 和 `TokenManager` 导入
  - 添加了API密钥配置
  - 重写了所有API调用方法
  - 添加了配置验证

#### 3. **文档文件**
- **`字幕功能环境配置说明.md`**: 更新了认证机制说明

### **新的认证架构**

#### **TTS服务 (保持不变)**
```typescript
// 使用用户登录token
AuthService.withTokenRefresh(async () => {
  const response = await fetch(ttsApiUrl, {
    headers: {
      'Authorization': `Bearer ${userToken}`,
    }
  })
})
```

#### **字幕服务 (新架构)**
```typescript
// 使用API密钥认证
const response = await fetch(subtitleApiUrl, {
  headers: {
    'Authorization': `Bearer ${SUBTITLE_API_CONFIG.API_KEY}`,
  }
})
```

## 🧪 验证步骤

### **1. 配置验证**
启动应用后，在浏览器控制台应该看到：
```
✅ 字幕API配置: {
  baseUrl: "http://localhost:8000",
  apiKeyConfigured: true,
  apiKeyPrefix: "OwxMwzoo...",
  timeout: 30000,
  maxRetries: 3
}
```

### **2. 网络请求验证**
上传文件时，应该看到：
- ✅ **请求地址**: `http://localhost:8000/api/v1/transcribe/upload`
- ✅ **请求头**: `Authorization: Bearer OwxMwzoo5YieYWaPQUFd1kPg8h0k0rXecEGo6310`
- ❌ **不应该有**: 向 `ttssapi.aispeak.top` 的token刷新请求

### **3. 错误处理验证**
如果字幕后端不可用，应该：
- ✅ 显示字幕服务相关的错误信息
- ❌ 不会触发TTS token刷新
- ❌ 不会显示"Authentication failed - refresh token expired"

## 🔍 预期行为变化

### **修改前 (有问题)**
```
用户点击生成 
→ SubtitleService.uploadAudio()
→ AuthService.withTokenRefresh() 
→ 发送到 localhost:8000 (带用户token)
→ 字幕后端认证失败
→ 触发TTS token刷新到 ttssapi.aispeak.top
→ 刷新失败，显示认证错误 ❌
```

### **修改后 (正确)**
```
用户点击生成
→ SubtitleService.uploadAudio()
→ 直接发送到 localhost:8000 (带API密钥)
→ 字幕后端处理请求 ✅
```

## 📋 测试清单

### **基础功能测试**
- [ ] 应用启动无错误
- [ ] 字幕按钮正常显示
- [ ] 模态窗口正常打开
- [ ] 配置验证信息正确显示

### **文件上传测试**
- [ ] 文件选择正常工作
- [ ] 参数配置正常工作
- [ ] 点击生成按钮
- [ ] 网络请求发送到正确地址
- [ ] 使用正确的认证头

### **错误处理测试**
- [ ] 字幕后端不可用时的错误处理
- [ ] API密钥错误时的错误处理
- [ ] 网络错误时的错误处理

### **TTS功能回归测试**
- [ ] TTS语音合成功能正常
- [ ] TTS认证系统正常
- [ ] 任务中心正常工作
- [ ] 用户登录/登出正常

## 🚨 故障排除

### **如果仍然出现认证错误**
1. **检查环境变量**: 确认 `NEXT_PUBLIC_SUBTITLE_API_KEY` 已设置
2. **重启开发服务器**: `npm run dev`
3. **清除浏览器缓存**: 硬刷新页面
4. **检查字幕后端**: 确认 `localhost:8000` 服务正常运行

### **如果API密钥无效**
1. **确认密钥正确**: 检查README.md中的密钥
2. **检查字幕后端配置**: 确认后端接受该密钥
3. **查看后端日志**: 检查认证失败原因

### **如果影响了TTS功能**
1. **检查TTS API调用**: 确认仍使用 `AuthService.withTokenRefresh`
2. **检查用户token**: 确认登录状态正常
3. **回滚修改**: 如有必要，恢复之前的代码

## ✅ 成功标准

修改成功的标志：
- ✅ 字幕上传请求发送到 `localhost:8000`
- ✅ 使用API密钥认证，不使用用户token
- ✅ 不再出现TTS token刷新请求
- ✅ TTS功能完全不受影响
- ✅ 错误处理更加准确和用户友好
