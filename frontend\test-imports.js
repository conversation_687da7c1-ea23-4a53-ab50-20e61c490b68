// 简单的导入测试文件
// 用于验证所有导入是否正常工作

console.log('开始测试导入...');

try {
  // 测试类型文件导入
  const types = require('./components/SubtitleGenerator/types.ts');
  console.log('✅ types.ts 导入成功');
  console.log('CONSTANTS:', types.CONSTANTS);
  console.log('ERROR_MESSAGES:', types.ERROR_MESSAGES);
  console.log('SUPPORTED_FILE_FORMATS:', types.SUPPORTED_FILE_FORMATS);
  
} catch (error) {
  console.error('❌ types.ts 导入失败:', error.message);
}

try {
  // 测试服务文件导入
  const service = require('./lib/subtitle-service.ts');
  console.log('✅ subtitle-service.ts 导入成功');
  console.log('SubtitleService:', service.SubtitleService);
  
} catch (error) {
  console.error('❌ subtitle-service.ts 导入失败:', error.message);
}

console.log('导入测试完成');
