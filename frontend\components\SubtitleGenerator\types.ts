/**
 * 字幕生成功能相关的TypeScript类型定义
 */

// 支持的文件格式
export const SUPPORTED_AUDIO_FORMATS = [
  'audio/mp3', 'audio/mpeg',
  'audio/wav', 'audio/wave',
  'audio/flac',
  'audio/m4a', 'audio/mp4',
  'audio/aac',
  'audio/ogg'
] as const

export const SUPPORTED_VIDEO_FORMATS = [
  'video/mp4',
  'video/quicktime' // MOV
] as const

export const SUPPORTED_FILE_FORMATS = [
  ...SUPPORTED_AUDIO_FORMATS,
  // ...SUPPORTED_VIDEO_FORMATS // 暂时只支持音频格式
] as const

// 支持的语言代码
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'es', name: '<PERSON>spa<PERSON><PERSON>', flag: '🇪🇸' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
  { code: 'ru', name: 'Русский', flag: '🇷🇺' },
  { code: 'it', name: 'Italiano', flag: '🇮🇹' },
  { code: 'pt', name: 'Português', flag: '🇵🇹' }
] as const

// 时间戳粒度选项
export const TIMESTAMP_GRANULARITY_OPTIONS = [
  { value: 'word', label: '词级别', description: '为每个词生成时间戳' },
  { value: 'sentence', label: '句子级别', description: '为每个句子生成时间戳' }
] as const

// additional_formats 详细配置
export interface AdditionalFormatsConfig {
  format: 'srt' // 固定为srt
  max_characters_per_line: number // 每行最大字符数（1-200，默认50）
  include_speakers: boolean // 是否包含说话人标记（默认false）
  include_timestamps: boolean // 是否包含时间戳（默认true）
  segment_on_silence_longer_than_s: number // 静音分段阈值秒数（0.1-10.0，默认0.6）
  max_segment_duration_s: number // 最大段落时长秒数（1.0-30.0，默认5.0）
  max_segment_chars: number // 最大段落字符数（10-500，默认90）
}

// 字幕生成配置
export interface SubtitleConfig {
  language_code?: string
  tag_audio_events: boolean
  timestamps_granularity: 'word' | 'sentence'
  additional_formats: string // 必需参数，JSON字符串
}

// 文件信息
export interface FileInfo {
  filename: string
  size: number
  duration?: number
  mime_type: string
}

// 文件验证结果
export interface FileValidation {
  isValid: boolean
  error?: string
  warnings?: string[]
}

// SRT文件信息
export interface SRTFile {
  filename: string
  download_url: string
  content_type: string
  file_size: number
  duration?: number
}

// 任务状态
export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed'

// API响应类型
export interface UploadResponse {
  task_id: string
  status: TaskStatus
  message: string
  file_info: FileInfo
}

export interface StatusResponse {
  task_id: string
  status: TaskStatus
  progress: number
  message: string
  srt_files?: SRTFile[]
  processing_time?: number
  error?: string
}

// 组件状态
export type ComponentStep = 'upload' | 'config' | 'processing' | 'completed' | 'error'

export interface SubtitleGeneratorState {
  // UI状态
  isOpen: boolean
  currentStep: ComponentStep
  
  // 文件状态
  selectedFile: File | null
  fileValidation: FileValidation
  
  // 参数配置
  config: SubtitleConfig
  
  // 任务状态
  taskId: string | null
  progress: number
  status: TaskStatus
  result?: StatusResponse
  error?: string
  
  // 轮询控制
  isPolling: boolean
  pollingInterval?: NodeJS.Timeout
}

// 任务中心扩展类型
export interface SubtitleTask {
  taskId: string
  type: 'subtitle'
  createdAt: number
  status: 'processing' | 'complete' | 'failed'
  downloadUrl?: string
  srtFiles?: SRTFile[]
  fileName?: string
  fileSize?: number
  isRefreshing?: boolean
}

// 表单验证错误
export interface FormErrors {
  file?: string
  language_code?: string
  num_speakers?: string
  additional_formats?: string
}

// 常量定义
export const CONSTANTS = {
  MAX_FILE_SIZE: 52428800, // 50MB in bytes
  POLLING_INTERVAL: 2000, // 2秒
  MAX_POLLING_ATTEMPTS: 300, // 最大轮询次数 (10分钟)
  SUPPORTED_FILE_EXTENSIONS: [
    '.mp3', '.wav', '.flac', '.m4a', '.aac', '.ogg',
    // '.mp4', '.mov' // 暂时只支持音频格式
  ]
} as const

// 默认的 additional_formats 配置
export const DEFAULT_ADDITIONAL_FORMATS: AdditionalFormatsConfig = {
  format: 'srt',
  max_characters_per_line: 50,
  include_speakers: false,
  include_timestamps: true,
  segment_on_silence_longer_than_s: 0.6,
  max_segment_duration_s: 5.0,
  max_segment_chars: 90
}

// 默认配置
export const DEFAULT_CONFIG: SubtitleConfig = {
  language_code: 'en',
  tag_audio_events: true,
  timestamps_granularity: 'word',
  additional_formats: JSON.stringify([DEFAULT_ADDITIONAL_FORMATS])
}

// 错误消息
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: `文件大小不能超过 ${CONSTANTS.MAX_FILE_SIZE / 1024 / 1024}MB`,
  UNSUPPORTED_FORMAT: '不支持的文件格式',
  UPLOAD_FAILED: '文件上传失败',
  TASK_FAILED: '字幕生成失败',
  NETWORK_ERROR: '网络连接错误',
  POLLING_TIMEOUT: '任务处理超时',
  INVALID_CONFIG: '配置参数无效'
} as const
