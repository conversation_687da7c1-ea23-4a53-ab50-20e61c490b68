# 字幕生成功能集成测试清单

## 🔍 测试环节

### 1. 基础UI测试
- [ ] 主页导航栏显示字幕生成按钮
- [ ] 点击字幕生成按钮打开模态窗口
- [ ] 模态窗口显示正确的步骤指示器
- [ ] 模态窗口可以通过X按钮关闭

### 2. 文件上传测试
- [ ] 文件拖拽区域正常显示
- [ ] 支持拖拽上传文件
- [ ] 支持点击选择文件
- [ ] 文件格式验证正常工作
- [ ] 文件大小验证正常工作
- [ ] 显示正确的文件信息

### 3. 参数配置测试
- [ ] 语言选择下拉框正常工作
- [ ] 时间戳粒度选择正常工作
- [ ] 音频事件标记开关正常工作
- [ ] 说话人识别开关正常工作
- [ ] 说话人数量滑块正常工作
- [ ] 额外格式配置输入框正常工作
- [ ] 配置预览正确显示

### 4. API集成测试
- [ ] 文件上传API调用正常
- [ ] 任务状态查询API调用正常
- [ ] 文件下载API调用正常
- [ ] 错误处理正常工作
- [ ] Token刷新机制正常工作

### 5. 任务管理测试
- [ ] 字幕任务正确添加到任务中心
- [ ] 任务中心显示正确的任务类型图标
- [ ] 任务状态轮询正常工作
- [ ] 任务完成后显示下载按钮
- [ ] 下载功能正常工作

### 6. 用户体验测试
- [ ] 加载状态正确显示
- [ ] 进度条正确更新
- [ ] 错误信息正确显示
- [ ] 成功提示正确显示
- [ ] 响应式设计正常工作

## 🚨 已知问题

### 需要注意的事项
1. **API端点配置**: 确保后端API服务正在运行
2. **文件大小限制**: 测试大文件上传的处理
3. **网络错误处理**: 测试网络中断情况
4. **并发任务**: 测试多个任务同时处理

### 可能的改进点
1. **文件预览**: 添加音频文件预览功能
2. **批量处理**: 支持批量上传多个文件
3. **格式转换**: 支持更多输出格式
4. **历史记录**: 保存用户的配置历史

## 📝 测试步骤

### 基础功能测试
1. 启动前端开发服务器
2. 登录系统
3. 点击字幕生成按钮
4. 上传测试音频文件
5. 配置参数
6. 提交任务
7. 观察任务处理过程
8. 下载生成的字幕文件

### 错误场景测试
1. 上传不支持的文件格式
2. 上传超大文件
3. 网络中断情况
4. 无效的配置参数
5. 后端服务不可用

## 🎯 成功标准

功能被认为成功集成当：
- [ ] 所有UI组件正常渲染
- [ ] 文件上传和验证正常工作
- [ ] 参数配置正确传递给API
- [ ] 任务状态正确更新
- [ ] 文件下载功能正常
- [ ] 错误处理用户友好
- [ ] 不影响现有TTS功能

## 🔧 调试信息

### 浏览器控制台检查
- 检查是否有JavaScript错误
- 检查网络请求是否正常
- 检查API响应格式

### 网络请求检查
- 上传请求格式正确
- 状态查询请求正常
- 下载请求正常
- 认证头正确传递

### 状态管理检查
- 组件状态正确更新
- 任务中心状态同步
- 模态窗口状态管理正常
