# API配置
# TTS服务API地址 - 生产环境请替换为你的 Cloudflare Worker 域名
NEXT_PUBLIC_API_URL=https://ttsapi.aispeak.top

# 字幕生成服务API地址 - 独立的字幕后端服务
# 本地开发环境
# NEXT_PUBLIC_SUBTITLE_API_URL=http://localhost:8000
# 生产环境示例
NEXT_PUBLIC_SUBTITLE_API_URL=https://subtitle-api.yourdomain.com

# 字幕生成服务API密钥 - 独立认证系统
# 请替换为您的实际API密钥
NEXT_PUBLIC_SUBTITLE_API_KEY=your_subtitle_api_key_here

# 备用后端API配置（可选）
# 当主API失败时自动切换到备用API
# 支持单个备用API：
# NEXT_PUBLIC_BACKUP_API_URL=https://tts-api-backup.aispeak.top
# 支持多个备用API（逗号分隔，按顺序重试）：
NEXT_PUBLIC_BACKUP_API_URL=https://backup1.aispeak.top,https://backup2.aispeak.top,https://backup3.aispeak.top
NEXT_PUBLIC_ENABLE_BACKUP_API=true

# 开发环境配置
# NEXT_PUBLIC_API_URL=http://localhost:8787
