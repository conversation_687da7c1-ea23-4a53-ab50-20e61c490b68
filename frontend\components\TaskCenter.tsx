"use client"

import React, { useState, useEffect, useCallback, useRef, forwardRef, useImperativeHandle } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  List,
  Search,
  X,
  Clock,
  Copy,
  Check,
  Download,
  RefreshCw,
  FileText,
  Mic
} from "lucide-react"
import { TokenManager } from "@/lib/api"
import { SubtitleService } from "@/lib/subtitle-service"
import type { SRTFile } from "@/components/SubtitleGenerator/types"

// 任务对象接口
interface Task {
  taskId: string;
  type: 'tts' | 'subtitle'; // 任务类型（必需）
  createdAt: number;
  status?: 'processing' | 'complete' | 'failed' | 'unknown';
  downloadUrl?: string;
  isRefreshing?: boolean;
  // 字幕任务特有字段
  srtFiles?: SRTFile[];
  fileName?: string; // 原始文件名
  fileSize?: number; // 原始文件大小
}

// 组件Props接口
interface TaskCenterProps {
  className?: string;
  // 【新增】映射查询函数，用于刷新时获取正确的物理taskId
  getActualTaskIds?: (displayTaskId: string) => string[];
}

// 组件Ref接口 - 支持双模式
export interface TaskCenterRef {
  addTask: (taskId: string, type: 'tts' | 'subtitle') => void;
  updateTaskStatus: (taskId: string, status: 'processing' | 'complete' | 'failed', downloadUrl?: string, srtFiles?: SRTFile[]) => void;
}

// 本地存储键名
const STORAGE_KEY = 'tts_task_center_tasks';

// 任务中心组件
const TaskCenter = forwardRef<TaskCenterRef, TaskCenterProps>(({ className = "", getActualTaskIds }, ref) => {
  // 状态管理
  const [showTaskCenter, setShowTaskCenter] = useState(false)
  const [taskList, setTaskList] = useState<Task[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([])
  const [copiedTaskId, setCopiedTaskId] = useState<string | null>(null)

  // 保存到本地存储
  const saveTasksToStorage = useCallback((tasks: Task[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(tasks));
    } catch (error) {
      console.error('Failed to save tasks to localStorage:', error);
    }
  }, []);

  // 数据迁移函数 - 处理旧版本任务数据
  const migrateTaskData = useCallback((tasks: any[]): Task[] => {
    return tasks.map(task => ({
      ...task,
      type: task.type || 'tts', // 为旧任务设置默认类型为TTS
      srtFiles: task.srtFiles || undefined,
      fileName: task.fileName || undefined,
      fileSize: task.fileSize || undefined
    }));
  }, []);

  // 从本地存储加载
  useEffect(() => {
    try {
      const savedTasks = localStorage.getItem(STORAGE_KEY);
      if (savedTasks) {
        const rawTasks = JSON.parse(savedTasks);
        const migratedTasks = migrateTaskData(rawTasks);
        setTaskList(migratedTasks);

        // 如果数据被迁移，保存迁移后的数据
        if (JSON.stringify(rawTasks) !== JSON.stringify(migratedTasks)) {
          saveTasksToStorage(migratedTasks);
        }
      }
    } catch (error) {
      console.error('Failed to load tasks from localStorage:', error);
    }
  }, [migrateTaskData, saveTasksToStorage]);

  // 搜索过滤逻辑
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredTasks(taskList);
    } else {
      const query = searchQuery.toLowerCase().trim();
      const filtered = taskList.filter(task =>
        task.taskId.toLowerCase().includes(query)
      );
      setFilteredTasks(filtered);
    }
  }, [taskList, searchQuery]);

  // 添加新任务
  const addTask = useCallback((taskId: string, type: 'tts' | 'subtitle') => {
    const newTask: Task = {
      taskId,
      type,
      createdAt: Date.now(),
      status: 'processing'
    };

    setTaskList(prev => {
      const updatedTasks = [newTask, ...prev];
      saveTasksToStorage(updatedTasks);
      return updatedTasks;
    });
  }, [saveTasksToStorage]);

  // 更新任务状态
  const updateTaskStatus = useCallback((taskId: string, status: 'processing' | 'complete' | 'failed', downloadUrl?: string, srtFiles?: SRTFile[]) => {
    setTaskList(prev => {
      const updatedTasks = prev.map(task =>
        task.taskId === taskId
          ? { ...task, status, downloadUrl, srtFiles, isRefreshing: false }
          : task
      );
      saveTasksToStorage(updatedTasks);
      return updatedTasks;
    });
  }, [saveTasksToStorage]);

  // 暴露方法给父组件 - 简化后只保留核心功能
  useImperativeHandle(ref, () => ({
    addTask,
    updateTaskStatus
  }), [addTask, updateTaskStatus]);

  // 【双模式】获取任务状态 - 支持TTS和字幕任务
  const fetchTaskStatus = useCallback(async (displayTaskId: string) => {
    try {
      // 设置刷新状态
      setTaskList(prev => prev.map(task =>
        task.taskId === displayTaskId ? { ...task, isRefreshing: true } : task
      ));

      // 获取任务类型
      const currentTask = taskList.find(task => task.taskId === displayTaskId);
      if (!currentTask) {
        throw new Error('任务不存在');
      }

      const taskType = currentTask.type;

      let data: any;

      if (taskType === 'subtitle') {
        // 字幕任务：使用SubtitleService
        data = await SubtitleService.getTaskStatus(displayTaskId);
        console.log(`[TASK-CENTER] Subtitle task status:`, data);

        // 处理字幕任务状态
        if (data.status === 'completed' && data.srt_files) {
          updateTaskStatus(displayTaskId, 'complete', undefined, data.srt_files);
          console.log(`[TASK-CENTER] Updated subtitle task ${displayTaskId} to complete`);
        } else if (data.status === 'processing' || data.status === 'pending') {
          updateTaskStatus(displayTaskId, 'processing');
          console.log(`[TASK-CENTER] Updated subtitle task ${displayTaskId} to processing`);
        } else if (data.status === 'failed') {
          updateTaskStatus(displayTaskId, 'failed');
          console.log(`[TASK-CENTER] Updated subtitle task ${displayTaskId} to failed`);
        }
      } else {
        // TTS任务：使用原有逻辑
        const token = TokenManager.getAccessToken();
        if (!token) {
          throw new Error('未找到访问令牌，请重新登录');
        }

        // 获取实际需要查询的物理taskId列表
        const actualTaskIds = getActualTaskIds ? getActualTaskIds(displayTaskId) : [displayTaskId];

        let lastError: Error | null = null;
        let successData: any = null;

        // 尝试查询所有可能的物理taskId
        for (const physicalTaskId of actualTaskIds) {
          try {
            console.log(`[TASK-CENTER] Trying to fetch TTS status for: ${physicalTaskId}`);

            const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/tts/status/${physicalTaskId}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              lastError = new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
              console.log(`[TASK-CENTER] TTS task ${physicalTaskId} failed:`, lastError.message);
              continue; // 尝试下一个物理ID
            }

            const responseData = await response.json();

            // 如果找到了成功的任务，记录并跳出循环
            if (responseData.status === 'complete' || responseData.status === 'completed') {
              successData = responseData;
              console.log(`[TASK-CENTER] Found successful TTS task: ${physicalTaskId}`, responseData);
              break;
            } else if (responseData.status === 'processing') {
              // 如果还在处理中，也算是有效响应
              successData = responseData;
              console.log(`[TASK-CENTER] Found processing TTS task: ${physicalTaskId}`, responseData);
              break;
            } else {
              // 其他状态（如failed），继续尝试下一个
              lastError = new Error(`Task ${physicalTaskId} status: ${responseData.status}`);
              console.log(`[TASK-CENTER] TTS task ${physicalTaskId} not successful:`, responseData.status);
              continue;
            }
          } catch (error: any) {
            lastError = error;
            console.log(`[TASK-CENTER] Error fetching TTS task ${physicalTaskId}:`, error.message);
            continue; // 尝试下一个物理ID
          }
        }

        // 如果所有物理ID都尝试失败了，抛出最后一个错误
        if (!successData) {
          throw lastError || new Error('所有TTS任务ID查询都失败了');
        }

        data = successData;

        // 处理TTS任务状态
        if (data.status === 'complete' || data.status === 'completed') {
          // 优先使用API返回的下载链接，备用R2直链
          const downloadUrl = data.audioUrl || data.downloadUrl || `https://r2-assets.aispeak.top/audios/${displayTaskId}.mp3`;
          updateTaskStatus(displayTaskId, 'complete', downloadUrl);
        } else if (data.status === 'processing' || data.status === 'pending' || data.status === 'running') {
          updateTaskStatus(displayTaskId, 'processing');
        } else if (data.status === 'failed' || data.status === 'error') {
          updateTaskStatus(displayTaskId, 'failed');
        } else {
          // 未知状态，标记为失败
          updateTaskStatus(displayTaskId, 'failed');
        }
      }

    } catch (error: any) {
      console.error('[TASK-CENTER] Failed to fetch task status:', error);
      // 重置刷新状态，但保持原有状态
      setTaskList(prev => prev.map(task =>
        task.taskId === displayTaskId ? { ...task, isRefreshing: false } : task
      ));

      // 可以考虑显示错误提示给用户
      // 这里暂时只在控制台记录错误
    }
  }, [updateTaskStatus, getActualTaskIds, taskList]);

  // 复制任务ID
  const copyTaskId = useCallback(async (taskId: string) => {
    try {
      await navigator.clipboard.writeText(taskId);
      setCopiedTaskId(taskId);
      setTimeout(() => setCopiedTaskId(null), 2000);
    } catch (error) {
      console.error('Failed to copy task ID:', error);
    }
  }, []);

  // 【双模式】下载文件 - 支持TTS音频和字幕文件
  const downloadFile = useCallback(async (task: Task) => {
    try {
      console.log('[TASK-CENTER] Initiating download for task:', { taskId: task.taskId, type: task.type });

      if (task.type === 'subtitle' && task.srtFiles) {
        // 字幕文件下载
        for (const srtFile of task.srtFiles) {
          try {
            const blob = await SubtitleService.downloadSRT(task.taskId, srtFile.filename);

            // 创建下载链接
            const blobUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = srtFile.filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(blobUrl);

            // 文件下载完成
          } catch (error) {
            console.error(`[TASK-CENTER] Failed to download subtitle file ${srtFile.filename}:`, error);
          }
        }
      } else if (task.type === 'tts' && task.downloadUrl) {
        // TTS音频下载
        console.log('[TASK-CENTER] Downloading TTS audio:', task.downloadUrl);

        // 生成带时间戳的文件名
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        const fileName = `tts_${year}${month}${day}_${hours}${minutes}${seconds}.mp3`;

        // 使用安全的认证下载
        const token = TokenManager.getAccessToken();
        if (!token) {
          throw new Error('认证失败，请重新登录');
        }

        const response = await fetch(task.downloadUrl, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/octet-stream'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);

        // 创建下载链接
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl);

        console.log('[TASK-CENTER] TTS audio download completed');
      } else if (task.type === 'subtitle') {
        // 字幕任务但没有srtFiles，尝试重新获取状态
        try {
          const status = await SubtitleService.getTaskStatus(task.taskId);
          if (status.srt_files && status.srt_files.length > 0) {
            // 更新任务状态并重新下载
            updateTaskStatus(task.taskId, 'complete', undefined, status.srt_files);
            // 递归调用下载
            const updatedTask = { ...task, srtFiles: status.srt_files };
            await downloadFile(updatedTask);
            return;
          }
        } catch (error) {
          console.error('[TASK-CENTER] Failed to fetch subtitle status:', error);
        }
        throw new Error('字幕文件不可用，请刷新任务状态');
      } else {
        throw new Error('无可下载的文件');
      }
    } catch (error) {
      console.error('[TASK-CENTER] Download failed:', error);
      // 可以在这里添加用户友好的错误提示
    }
  }, []);

  // 清空所有任务
  const clearAllTasks = useCallback(() => {
    setTaskList([]);
    saveTasksToStorage([]);
  }, [saveTasksToStorage]);

  return (
    <>
      {/* 任务中心按钮 */}
      <button
        onClick={() => setShowTaskCenter(true)}
        className={`relative overflow-hidden px-4 py-2 text-sm font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group text-white bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 hover:from-purple-600 hover:via-purple-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm ${className}`}
      >
        {/* 渐变光效背景 */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 via-purple-500/20 to-indigo-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* 图标和文字 */}
        <List className="w-4 h-4 relative z-10" />
        <span className="relative z-10">任务中心</span>
        
        {/* 任务数量徽章 */}
        {taskList.length > 0 && (
          <span className="relative z-10 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center">
            {taskList.length > 99 ? '99+' : taskList.length}
          </span>
        )}
      </button>

      {/* 任务中心模态框 */}
      <Dialog open={showTaskCenter} onOpenChange={setShowTaskCenter}>
        <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl font-bold">
              <List className="w-5 h-5 text-purple-600" />
              任务中心
              <span className="text-sm font-normal text-gray-500">
                ({taskList.length} 个任务)
              </span>
            </DialogTitle>
          </DialogHeader>

          {/* 搜索和操作栏 */}
          <div className="flex items-center gap-3 py-4 border-b border-gray-100">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="搜索任务ID..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-10 border-2 border-gray-200 focus:border-purple-400 focus:ring-2 focus:ring-purple-50"
              />
            </div>
            <Button
              onClick={clearAllTasks}
              variant="outline"
              size="sm"
              className="h-10 px-4 border-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
            >
              <X className="w-4 h-4 mr-1" />
              清空
            </Button>
          </div>

          {/* 任务列表 */}
          <div className="flex-1 overflow-y-auto">
            {filteredTasks.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <List className="w-12 h-12 mb-4 text-gray-300" />
                <h3 className="text-lg font-semibold mb-2">暂无任务</h3>
                <p className="text-sm text-center max-w-sm">
                  {searchQuery ? "没有找到匹配的任务" : "您还没有创建任何任务，开始使用AI语音转换功能来创建您的第一个任务吧！"}
                </p>
              </div>
            ) : (
              <div className="space-y-3 p-1">
                {filteredTasks.map((task, index) => (
                  <TaskCard 
                    key={task.taskId} 
                    task={task} 
                    index={index}
                    onCopy={copyTaskId}
                    onDownload={downloadFile}
                    onRefresh={fetchTaskStatus}
                    copiedTaskId={copiedTaskId}
                  />
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
});

TaskCenter.displayName = 'TaskCenter';

// 任务卡片组件Props接口
interface TaskCardProps {
  task: Task;
  index: number;
  onCopy: (taskId: string) => void;
  onDownload: (task: Task) => void;
  onRefresh: (taskId: string) => void;
  copiedTaskId: string | null;
}

// 时间格式化函数
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '刚刚';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}分钟前`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}小时前`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}天前`;
  }
};

// 状态样式获取函数
const getStatusStyle = (status: string): string => {
  switch (status) {
    case 'processing':
      return 'bg-orange-100 text-orange-600';
    case 'complete':
      return 'bg-green-100 text-green-600';
    case 'failed':
      return 'bg-red-100 text-red-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

// 状态文本获取函数
const getStatusText = (status: string): string => {
  switch (status) {
    case 'processing':
      return '处理中';
    case 'complete':
      return '已完成';
    case 'failed':
      return '失败';
    default:
      return '未知';
  }
};

// 任务卡片组件
const TaskCard: React.FC<TaskCardProps> = ({
  task,
  index,
  onCopy,
  onDownload,
  onRefresh,
  copiedTaskId
}) => (
  <div className="bg-white border-2 border-gray-100 rounded-xl p-4 hover:border-purple-200 hover:shadow-md transition-all duration-200">
    <div className="flex items-start justify-between">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-lg">
            #{index + 1}
          </span>
          {/* 任务类型标识 */}
          <span className={`text-xs font-medium px-2 py-1 rounded-lg flex items-center gap-1 ${
            task.type === 'subtitle'
              ? 'text-indigo-600 bg-indigo-100'
              : 'text-emerald-600 bg-emerald-100'
          }`}>
            {task.type === 'subtitle' ? (
              <>
                <FileText className="w-3 h-3" />
                字幕生成
              </>
            ) : (
              <>
                <Mic className="w-3 h-3" />
                语音合成
              </>
            )}
          </span>
          <span className="text-xs text-gray-500 flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {formatTime(task.createdAt)}
          </span>
          {/* 状态指示器 */}
          {task.status && (
            <span className={`text-xs px-2 py-1 rounded-lg font-medium ${getStatusStyle(task.status)}`}>
              {getStatusText(task.status)}
            </span>
          )}
        </div>

        {/* 任务ID */}
        <div className="flex items-center gap-2 mb-3">
          <span className="text-sm font-mono text-gray-700 bg-gray-50 px-2 py-1 rounded border truncate max-w-[700px]">
            {task.taskId}
          </span>
          <button
            onClick={() => onCopy(task.taskId)}
            className="p-1 rounded hover:bg-gray-100 transition-colors duration-200"
            title="复制任务ID"
          >
            {copiedTaskId === task.taskId ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3 text-gray-400" />
            )}
          </button>
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div className="flex items-center gap-2 ml-4">
        {/* 下载按钮 */}
        {task.status === 'complete' && (task.downloadUrl || task.srtFiles) && (
          <button
            onClick={() => onDownload(task)}
            className="p-2 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 transition-all duration-200"
            title={task.type === 'subtitle' ? '下载字幕文件' : '下载音频'}
          >
            <Download className="w-4 h-4" />
          </button>
        )}

        {/* 刷新按钮 */}
        <button
          onClick={() => onRefresh(task.taskId)}
          disabled={task.isRefreshing}
          className={`p-2 rounded-lg transition-all duration-200 flex items-center justify-center ${
            task.isRefreshing
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 cursor-pointer'
          }`}
          title="刷新任务状态"
        >
          <RefreshCw className={`w-4 h-4 ${task.isRefreshing ? 'animate-spin' : ''}`} />
        </button>
      </div>
    </div>
  </div>
);

export default TaskCenter;
